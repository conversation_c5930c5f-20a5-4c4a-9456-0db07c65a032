package email

import (
	"context"
	"fmt"
	"io"
	"mime"
	"mime/multipart"
	"net/mail"
	"path/filepath"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/xuri/excelize/v2"
	"github.com/philippgille/chromem-go"
	"github.com/tmc/langchaingo/llms"
	"github.com/tmc/langchaingo/llms/ollama"
)

// 📧 Email Analysis Service - Comprehensive Email & Attachment Processing
type EmailAnalysisService struct {
	log        *log.Helper
	vectorDB   *chromem.DB
	llm        llms.Model
	config     *EmailAnalysisConfig
}

// 📊 Email Analysis Configuration
type EmailAnalysisConfig struct {
	MaxAttachmentSize int64  `json:"max_attachment_size"`
	SupportedFormats  []string `json:"supported_formats"`
	OllamaURL        string `json:"ollama_url"`
	VectorDBPath     string `json:"vector_db_path"`
}

// 📨 Email Analysis Result
type EmailAnalysisResult struct {
	EmailID          string                    `json:"email_id"`
	Subject          string                    `json:"subject"`
	From             string                    `json:"from"`
	To               []string                  `json:"to"`
	Timestamp        time.Time                 `json:"timestamp"`
	BodyAnalysis     *TextAnalysis             `json:"body_analysis"`
	AttachmentCount  int                       `json:"attachment_count"`
	Attachments      []*AttachmentAnalysis     `json:"attachments"`
	Sentiment        string                    `json:"sentiment"`
	SentimentScore   float64                   `json:"sentiment_score"`
	Priority         string                    `json:"priority"`
	Category         string                    `json:"category"`
	ActionItems      []string                  `json:"action_items"`
	HVACRelevance    *HVACRelevanceAnalysis    `json:"hvac_relevance"`
	VectorEmbedding  []float32                 `json:"vector_embedding,omitempty"`
}

// 📄 Text Analysis
type TextAnalysis struct {
	Content      string    `json:"content"`
	WordCount    int       `json:"word_count"`
	Language     string    `json:"language"`
	KeyPhrases   []string  `json:"key_phrases"`
	Summary      string    `json:"summary"`
	Entities     []string  `json:"entities"`
}

// 📎 Attachment Analysis
type AttachmentAnalysis struct {
	Filename     string        `json:"filename"`
	ContentType  string        `json:"content_type"`
	Size         int64         `json:"size"`
	Format       string        `json:"format"`
	TextContent  string        `json:"text_content,omitempty"`
	Analysis     *TextAnalysis `json:"analysis,omitempty"`
	ExcelData    *ExcelData    `json:"excel_data,omitempty"`
	IsProcessed  bool          `json:"is_processed"`
	Error        string        `json:"error,omitempty"`
}

// 📊 Excel Data Structure
type ExcelData struct {
	Sheets    []string              `json:"sheets"`
	RowCount  int                   `json:"row_count"`
	ColCount  int                   `json:"col_count"`
	Headers   []string              `json:"headers"`
	Summary   string                `json:"summary"`
	Data      []map[string]string   `json:"data,omitempty"`
}

// 🔧 HVAC Relevance Analysis
type HVACRelevanceAnalysis struct {
	IsHVACRelated    bool     `json:"is_hvac_related"`
	Confidence       float64  `json:"confidence"`
	HVACKeywords     []string `json:"hvac_keywords"`
	ServiceType      string   `json:"service_type"`
	Urgency          string   `json:"urgency"`
	EstimatedCost    string   `json:"estimated_cost,omitempty"`
	RecommendedAction string  `json:"recommended_action"`
}

// NewEmailAnalysisService creates a new email analysis service
func NewEmailAnalysisService(config *EmailAnalysisConfig, logger log.Logger) (*EmailAnalysisService, error) {
	log := log.NewHelper(logger)
	
	// Initialize Vector Database
	vectorDB := chromem.NewDB()
	
	// Initialize Ollama LLM
	llm, err := ollama.New(ollama.WithServerURL(config.OllamaURL))
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Ollama: %w", err)
	}
	
	return &EmailAnalysisService{
		log:      log,
		vectorDB: vectorDB,
		llm:      llm,
		config:   config,
	}, nil
}

// 🔍 AnalyzeEmail - Main email analysis function
func (s *EmailAnalysisService) AnalyzeEmail(ctx context.Context, emailData []byte) (*EmailAnalysisResult, error) {
	s.log.WithContext(ctx).Info("Starting comprehensive email analysis")
	
	// Parse email
	msg, err := mail.ReadMessage(strings.NewReader(string(emailData)))
	if err != nil {
		return nil, fmt.Errorf("failed to parse email: %w", err)
	}
	
	result := &EmailAnalysisResult{
		EmailID:   generateEmailID(),
		Subject:   msg.Header.Get("Subject"),
		From:      msg.Header.Get("From"),
		To:        strings.Split(msg.Header.Get("To"), ","),
		Timestamp: time.Now(),
	}
	
	// Parse multipart message for attachments
	mediaType, params, err := mime.ParseMediaType(msg.Header.Get("Content-Type"))
	if err == nil && strings.HasPrefix(mediaType, "multipart/") {
		err = s.processMultipartMessage(ctx, msg.Body, params["boundary"], result)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Failed to process multipart message: %v", err)
		}
	} else {
		// Single part message
		body, err := io.ReadAll(msg.Body)
		if err != nil {
			return nil, fmt.Errorf("failed to read email body: %w", err)
		}
		result.BodyAnalysis = s.analyzeText(ctx, string(body))
	}
	
	// Perform AI analysis
	err = s.performAIAnalysis(ctx, result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("AI analysis failed: %v", err)
	}
	
	// Store in vector database
	err = s.storeInVectorDB(ctx, result)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Failed to store in vector DB: %v", err)
	}
	
	s.log.WithContext(ctx).Infof("Email analysis completed for: %s", result.Subject)
	return result, nil
}

// 📎 Process multipart message with attachments
func (s *EmailAnalysisService) processMultipartMessage(ctx context.Context, body io.Reader, boundary string, result *EmailAnalysisResult) error {
	mr := multipart.NewReader(body, boundary)
	
	for {
		part, err := mr.NextPart()
		if err == io.EOF {
			break
		}
		if err != nil {
			return err
		}
		
		disposition := part.Header.Get("Content-Disposition")
		if strings.Contains(disposition, "attachment") || strings.Contains(disposition, "inline") {
			// Process attachment
			attachment, err := s.processAttachment(ctx, part)
			if err != nil {
				s.log.WithContext(ctx).Warnf("Failed to process attachment: %v", err)
				continue
			}
			result.Attachments = append(result.Attachments, attachment)
			result.AttachmentCount++
		} else {
			// Process email body
			bodyContent, err := io.ReadAll(part)
			if err != nil {
				continue
			}
			if result.BodyAnalysis == nil {
				result.BodyAnalysis = s.analyzeText(ctx, string(bodyContent))
			}
		}
	}
	
	return nil
}

// 📎 Process individual attachment
func (s *EmailAnalysisService) processAttachment(ctx context.Context, part *multipart.Part) (*AttachmentAnalysis, error) {
	filename := part.FileName()
	if filename == "" {
		filename = "unnamed_attachment"
	}
	
	contentType := part.Header.Get("Content-Type")
	data, err := io.ReadAll(part)
	if err != nil {
		return nil, err
	}
	
	attachment := &AttachmentAnalysis{
		Filename:    filename,
		ContentType: contentType,
		Size:        int64(len(data)),
		Format:      strings.ToLower(filepath.Ext(filename)),
	}
	
	// Check size limit
	if attachment.Size > s.config.MaxAttachmentSize {
		attachment.Error = "File too large"
		return attachment, nil
	}
	
	// Process based on file type
	switch attachment.Format {
	case ".xlsx", ".xls":
		err = s.processExcelFile(ctx, data, attachment)
	case ".txt", ".csv":
		attachment.TextContent = string(data)
		attachment.Analysis = s.analyzeText(ctx, attachment.TextContent)
	case ".pdf":
		// PDF processing would go here
		attachment.Error = "PDF processing not implemented yet"
	default:
		attachment.Error = "Unsupported file format"
	}
	
	if err != nil {
		attachment.Error = err.Error()
	} else {
		attachment.IsProcessed = true
	}
	
	return attachment, nil
}

// 📊 Process Excel files
func (s *EmailAnalysisService) processExcelFile(ctx context.Context, data []byte, attachment *AttachmentAnalysis) error {
	// Create temporary file for excelize
	f, err := excelize.OpenReader(strings.NewReader(string(data)))
	if err != nil {
		return err
	}
	defer f.Close()
	
	excelData := &ExcelData{
		Sheets: f.GetSheetList(),
	}
	
	// Process first sheet
	if len(excelData.Sheets) > 0 {
		sheetName := excelData.Sheets[0]
		rows, err := f.GetRows(sheetName)
		if err != nil {
			return err
		}
		
		excelData.RowCount = len(rows)
		if len(rows) > 0 {
			excelData.ColCount = len(rows[0])
			excelData.Headers = rows[0]
			
			// Extract sample data (first 10 rows)
			for i, row := range rows {
				if i == 0 || i > 10 {
					continue
				}
				rowData := make(map[string]string)
				for j, cell := range row {
					if j < len(excelData.Headers) {
						rowData[excelData.Headers[j]] = cell
					}
				}
				excelData.Data = append(excelData.Data, rowData)
			}
		}
		
		excelData.Summary = fmt.Sprintf("Excel file with %d sheets, %d rows, %d columns", 
			len(excelData.Sheets), excelData.RowCount, excelData.ColCount)
	}
	
	attachment.ExcelData = excelData
	return nil
}

// 📝 Analyze text content
func (s *EmailAnalysisService) analyzeText(ctx context.Context, content string) *TextAnalysis {
	words := strings.Fields(content)
	
	return &TextAnalysis{
		Content:    content,
		WordCount:  len(words),
		Language:   "en", // Simple detection
		KeyPhrases: s.extractKeyPhrases(content),
		Summary:    s.generateSummary(content),
		Entities:   s.extractEntities(content),
	}
}

// 🔑 Extract key phrases (simple implementation)
func (s *EmailAnalysisService) extractKeyPhrases(content string) []string {
	// Simple keyword extraction - in production, use more sophisticated NLP
	hvacKeywords := []string{"HVAC", "air conditioning", "heating", "cooling", "repair", "maintenance", "installation"}
	var found []string
	
	contentLower := strings.ToLower(content)
	for _, keyword := range hvacKeywords {
		if strings.Contains(contentLower, strings.ToLower(keyword)) {
			found = append(found, keyword)
		}
	}
	
	return found
}

// 📄 Generate summary (placeholder)
func (s *EmailAnalysisService) generateSummary(content string) string {
	if len(content) > 200 {
		return content[:200] + "..."
	}
	return content
}

// 🏷️ Extract entities (placeholder)
func (s *EmailAnalysisService) extractEntities(content string) []string {
	// Simple entity extraction - in production, use NER models
	return []string{}
}

// 🤖 Perform AI analysis using Ollama
func (s *EmailAnalysisService) performAIAnalysis(ctx context.Context, result *EmailAnalysisResult) error {
	if result.BodyAnalysis == nil {
		return nil
	}
	
	// Sentiment analysis
	sentiment, score := s.analyzeSentiment(ctx, result.BodyAnalysis.Content)
	result.Sentiment = sentiment
	result.SentimentScore = score
	
	// HVAC relevance analysis
	result.HVACRelevance = s.analyzeHVACRelevance(ctx, result.BodyAnalysis.Content)
	
	// Priority and category
	result.Priority = s.determinePriority(ctx, result)
	result.Category = s.categorizeEmail(ctx, result)
	
	// Extract action items
	result.ActionItems = s.extractActionItems(ctx, result.BodyAnalysis.Content)
	
	return nil
}

// 💭 Analyze sentiment
func (s *EmailAnalysisService) analyzeSentiment(ctx context.Context, content string) (string, float64) {
	// Simple sentiment analysis - in production, use AI models
	positiveWords := []string{"good", "great", "excellent", "satisfied", "happy"}
	negativeWords := []string{"bad", "terrible", "awful", "unsatisfied", "angry", "problem", "issue"}
	
	contentLower := strings.ToLower(content)
	positiveCount := 0
	negativeCount := 0
	
	for _, word := range positiveWords {
		if strings.Contains(contentLower, word) {
			positiveCount++
		}
	}
	
	for _, word := range negativeWords {
		if strings.Contains(contentLower, word) {
			negativeCount++
		}
	}
	
	if positiveCount > negativeCount {
		return "positive", 0.7
	} else if negativeCount > positiveCount {
		return "negative", 0.7
	}
	
	return "neutral", 0.5
}

// 🔧 Analyze HVAC relevance
func (s *EmailAnalysisService) analyzeHVACRelevance(ctx context.Context, content string) *HVACRelevanceAnalysis {
	hvacKeywords := []string{"hvac", "air conditioning", "heating", "cooling", "repair", "maintenance", "installation", "thermostat", "duct", "filter"}
	urgencyKeywords := []string{"urgent", "emergency", "asap", "immediately", "broken", "not working"}
	
	contentLower := strings.ToLower(content)
	foundKeywords := []string{}
	urgencyFound := false
	
	for _, keyword := range hvacKeywords {
		if strings.Contains(contentLower, keyword) {
			foundKeywords = append(foundKeywords, keyword)
		}
	}
	
	for _, keyword := range urgencyKeywords {
		if strings.Contains(contentLower, keyword) {
			urgencyFound = true
			break
		}
	}
	
	isRelevant := len(foundKeywords) > 0
	confidence := float64(len(foundKeywords)) / float64(len(hvacKeywords))
	
	urgency := "normal"
	if urgencyFound {
		urgency = "high"
	}
	
	return &HVACRelevanceAnalysis{
		IsHVACRelated:     isRelevant,
		Confidence:        confidence,
		HVACKeywords:      foundKeywords,
		ServiceType:       s.determineServiceType(foundKeywords),
		Urgency:           urgency,
		RecommendedAction: s.recommendAction(isRelevant, urgency),
	}
}

// Helper functions
func (s *EmailAnalysisService) determinePriority(ctx context.Context, result *EmailAnalysisResult) string {
	if result.HVACRelevance != nil && result.HVACRelevance.Urgency == "high" {
		return "high"
	}
	if result.Sentiment == "negative" {
		return "medium"
	}
	return "normal"
}

func (s *EmailAnalysisService) categorizeEmail(ctx context.Context, result *EmailAnalysisResult) string {
	if result.HVACRelevance != nil && result.HVACRelevance.IsHVACRelated {
		return "hvac_service"
	}
	return "general"
}

func (s *EmailAnalysisService) extractActionItems(ctx context.Context, content string) []string {
	// Simple action item extraction
	actionWords := []string{"schedule", "call", "visit", "repair", "replace", "install"}
	contentLower := strings.ToLower(content)
	var actions []string
	
	for _, word := range actionWords {
		if strings.Contains(contentLower, word) {
			actions = append(actions, fmt.Sprintf("Action needed: %s", word))
		}
	}
	
	return actions
}

func (s *EmailAnalysisService) determineServiceType(keywords []string) string {
	for _, keyword := range keywords {
		switch keyword {
		case "repair":
			return "repair"
		case "installation", "install":
			return "installation"
		case "maintenance":
			return "maintenance"
		}
	}
	return "general"
}

func (s *EmailAnalysisService) recommendAction(isRelevant bool, urgency string) string {
	if !isRelevant {
		return "No HVAC action required"
	}
	
	if urgency == "high" {
		return "Schedule emergency service call immediately"
	}
	
	return "Schedule service appointment within 24-48 hours"
}

// 🗄️ Store in vector database
func (s *EmailAnalysisService) storeInVectorDB(ctx context.Context, result *EmailAnalysisResult) error {
	if result.BodyAnalysis == nil {
		return nil
	}
	
	// Create document for vector storage
	doc := chromem.Document{
		ID:       result.EmailID,
		Content:  result.BodyAnalysis.Content,
		Metadata: map[string]any{
			"subject":    result.Subject,
			"from":       result.From,
			"timestamp":  result.Timestamp,
			"sentiment":  result.Sentiment,
			"category":   result.Category,
			"priority":   result.Priority,
		},
	}
	
	// Add to vector database
	err := s.vectorDB.AddDocuments(ctx, []chromem.Document{doc}, nil)
	if err != nil {
		return fmt.Errorf("failed to add document to vector DB: %w", err)
	}
	
	return nil
}

// 🔍 Search similar emails
func (s *EmailAnalysisService) SearchSimilarEmails(ctx context.Context, query string, limit int) ([]chromem.Document, error) {
	results, err := s.vectorDB.Query(ctx, query, limit, nil, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to search vector DB: %w", err)
	}
	
	return results, nil
}

// 🆔 Generate unique email ID
func generateEmailID() string {
	return fmt.Sprintf("email_%d", time.Now().UnixNano())
}
