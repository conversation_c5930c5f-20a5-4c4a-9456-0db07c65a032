GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)
INTERNAL_PROTO_FILES=$(shell find internal -name *.proto)
API_PROTO_FILES=$(shell find api -name *.proto)

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/wire/cmd/wire@latest

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)

.PHONY: api
# generate api proto
api:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
	       $(API_PROTO_FILES)

.PHONY: build
# build
build:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./...

.PHONY: generate
# generate
generate:
	go mod tidy
	go get github.com/google/wire/cmd/wire@latest
	go generate ./...

.PHONY: wire
# generate wire
wire:
	cd cmd/server && wire

.PHONY: all
# generate all
all:
	make api;
	make config;
	make generate;

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.DEFAULT_GOAL := help

# 📧 Email Intelligence Service targets

.PHONY: build-email
# build email intelligence service
build-email:
	mkdir -p bin/ && go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/email-intelligence ./cmd/email-intelligence

.PHONY: run-email
# run email intelligence service
run-email:
	./bin/email-intelligence -conf configs/email-intelligence.yaml

.PHONY: email-intelligence
# build and run email intelligence service
email-intelligence: build-email run-email

.PHONY: docker-build-email
# build email intelligence with docker
docker-build-email:
	docker run --rm -v $(PWD):/workspace -w /workspace golang:1.24 go build -o email-intelligence ./cmd/email-intelligence

.PHONY: docker-run-email
# run email intelligence with docker
docker-run-email:
	docker run --rm -v $(PWD):/workspace -w /workspace -p 8082:8082 golang:1.24 ./email-intelligence -conf configs/email-intelligence.yaml

.PHONY: test-email
# test email intelligence components
test-email:
	go test -v ./internal/email/... -cover

.PHONY: setup-email-env
# setup email intelligence environment
setup-email-env:
	@echo "Setting up Email Intelligence environment..."
	mkdir -p data/email_vectors
	mkdir -p data/attachments
	mkdir -p logs
	@echo "Environment setup complete!"

.PHONY: start-ollama
# start ollama for AI analysis
start-ollama:
	@echo "Starting Ollama service..."
	docker run -d --name ollama -p 11434:11434 ollama/ollama
	@echo "Ollama started on http://localhost:11434"

.PHONY: pull-gemma
# pull gemma model for ollama
pull-gemma:
	@echo "Pulling Gemma model..."
	docker exec ollama ollama pull gemma:7b
	@echo "Gemma model ready!"

.PHONY: setup-email-complete
# complete email intelligence setup
setup-email-complete: setup-email-env start-ollama pull-gemma
	@echo "🎉 Email Intelligence setup complete!"
	@echo "📧 Ready to analyze emails with AI!"

.PHONY: demo-email
# quick demo of email intelligence
demo-email:
	@echo "🚀 Starting Email Intelligence Demo..."
	@echo "📧 Building service..."
	make build-email
	@echo "🌐 Starting service on http://localhost:8082..."
	@echo "📊 Dashboard: http://localhost:8082/api/v1/email-analysis/dashboard/stats"
	@echo "🔍 Health: http://localhost:8082/health"
	./bin/email-intelligence -conf configs/email-intelligence.yaml

.PHONY: dev-email
# email intelligence development mode
dev-email:
	@echo "🔧 Starting Email Intelligence in development mode..."
	go run ./cmd/email-intelligence -conf configs/email-intelligence.yaml